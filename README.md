# SLT - Smart Language Tool for Database Analysis

مشروع ذكي لتحليل قواعد البيانات باستخدام Google Gemini AI. يقوم المشروع بتحليل بنية قواعد البيانات وتوليد أوصاف ذكية للجداول والعلاقات، مع إمكانية الاستعلام بالغة الطبيعية.

## المميزات

- 🤖 **تحليل ذكي للجداول**: استخدام Google Gemini لتوليد أوصاف دقيقة للجداول
- 🔍 **البحث الشعاعي**: إيجاد الجداول المناسبة بناءً على سؤال المستخدم
- 💬 **استعلام بالغة الطبيعية**: تحويل الأسئلة إلى استعلامات SQL
- 🗄️ **دعم متعدد لقواعد البيانات**: MySQL و SQL Server
- 📊 **واجهة تفاعلية**: عرض النتائج بشكل جذاب ومفهوم

## متطلبات التشغيل

- Node.js 18 أو أحدث
- Google Gemini API Key
- قاعدة بيانات MySQL أو SQL Server

## التثبيت والإعداد

1. **استنساخ المشروع**:
```bash
git clone <repository-url>
cd slt
```

2. **تثبيت التبعيات**:
```bash
npm install
```

3. **إعداد متغيرات البيئة**:
```bash
cp .env.example .env.local
```

4. **تحديث ملف `.env.local`**:
```env
GEMINI_API_KEY=your_gemini_api_key_here
```

احصل على API Key من [Google AI Studio](https://aistudio.google.com/apikey)

5. **تشغيل المشروع**:

```bash
npm run dev
```

افتح [http://localhost:3000](http://localhost:3000) في المتصفح لرؤية التطبيق.

## كيفية الاستخدام

1. **اتصال بقاعدة البيانات**: أدخل بيانات الاتصال بقاعدة البيانات
2. **تحليل البنية**: انتظر حتى يتم تحليل جداول قاعدة البيانات
3. **طرح الأسئلة**: اكتب سؤالك بالغة الطبيعية
4. **عرض النتائج**: شاهد الاستعلام المولد والنتائج

## التقنيات المستخدمة

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **AI**: Google Gemini 2.0 Flash
- **Database**: MySQL2, MSSQL
- **UI Components**: Radix UI, Lucide React, Recharts

## الهيكل العام للمشروع

```
src/
├── app/                 # صفحات Next.js
├── components/          # مكونات React
├── lib/
│   ├── ai/             # منطق الذكاء الاصطناعي
│   ├── agent/          # وكيل قاعدة البيانات
│   └── database/       # إدارة قواعد البيانات
```

## التحديثات الأخيرة

✅ **تم الانتقال من OpenAI إلى Google Gemini**
- استبدال OpenAI API بـ Google Gemini API
- تحسين جودة تحليل الجداول
- تحسين دقة توليد استعلامات SQL
- دعم أفضل للغة العربية

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
