"use client"

import React, { useState, useEffect } from 'react';
import { DatabaseConnection } from '@/lib/database/types';
import { ChatInterface, ChatMessage } from '@/components/chat';
import { DatabaseConfig, DatabaseInfo } from '@/components/database';
import { SchemaProgress } from '@/components/progress';
import { ResultsDisplay } from '@/components/results';
import { Brain, Database, MessageSquare, BarChart3 } from 'lucide-react';

// أنواع البيانات للواجهة الأمامية
interface AgentQueryResult {
  query: string;
  explanation: string;
  confidence: number;
  relevantTables: string[];
  executionTime?: number;
  results?: any[];
  error?: string;
  intelligentSummary?: any;
}

interface AgentState {
  isInitialized: boolean;
  hasSchema: boolean;
  totalTables: number;
  schemaLastUpdated?: string;
  databaseType?: 'mysql' | 'mssql';
}

type AppState = 'setup' | 'connecting' | 'ready' | 'error';

export default function Home() {
  const [appState, setAppState] = useState<AppState>('setup');
  const [agentState, setAgentState] = useState<AgentState>({
    isInitialized: false,
    hasSchema: false,
    totalTables: 0
  });
  const [currentConnection, setCurrentConnection] = useState<DatabaseConnection | null>(null);
  const [showProgress, setShowProgress] = useState(false);
  const [activeTab, setActiveTab] = useState<'chat' | 'database' | 'results'>('chat');
  const [lastQueryResult, setLastQueryResult] = useState<AgentQueryResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [tablesInfo, setTablesInfo] = useState<Array<{
    name: string;
    description: string;
    domain: string;
    columnsCount: number;
    rowCount?: number;
  }>>([]);

  // فحص Schema الموجود عند تحميل الصفحة
  useEffect(() => {
    console.log('🚀 useEffect تم تشغيله - فحص Schema...');

    const checkExistingData = async () => {
      try {
        console.log('📡 إرسال طلب فحص Schema...');
        const response = await fetch('/api/database/check-schema');
        const result = await response.json();

        console.log('📋 نتيجة فحص Schema:', result);

        if (result.exists && result.upToDate) {
          console.log('✅ تم العثور على Schema محدث، تهيئة الوكيل...');

          // تحميل معلومات الاتصال المحفوظة
          try {
            console.log('📡 تحميل معلومات الاتصال...');
            const connResponse = await fetch('/api/database/get-connection');
            if (connResponse.ok) {
              const connData = await connResponse.json();
              if (connData.connection) {
                console.log('✅ تم تحميل معلومات الاتصال:', connData.connection);

                // تحديث جميع الـ states دفعة واحدة
                setCurrentConnection(connData.connection);
                setTablesInfo(result.tablesInfo || []);
                setAgentState({
                  isInitialized: true,
                  hasSchema: true,
                  totalTables: result.tablesCount || 0
                });
                setAppState('ready');

                console.log('🎉 تم تحديث جميع الحالات - الوكيل جاهز!');
              } else {
                console.log('❌ لا توجد معلومات اتصال في الرد');
              }
            } else {
              console.log('❌ فشل في تحميل معلومات الاتصال');
            }
          } catch (error) {
            console.log('❌ خطأ في تحميل معلومات الاتصال:', error);
          }
        } else {
          console.log('❌ Schema غير موجود أو قديم');
        }
      } catch (error) {
        console.log('❌ خطأ في فحص Schema:', error);
      }
    };

    checkExistingData();
  }, []); // تشغيل مرة واحدة فقط عند التحميل

  // اختبار الاتصال بقاعدة البيانات
  const handleTestConnection = async (connection: DatabaseConnection): Promise<boolean> => {
    try {
      const response = await fetch('/api/database/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(connection),
      });

      const result = await response.json();
      return result.success;
    } catch (error) {
      console.error('خطأ في اختبار الاتصال:', error);
      return false;
    }
  };

  // فحص ما إذا كان Schema موجود ومحدث
  const checkExistingSchema = async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/database/check-schema');
      const result = await response.json();
      return result.exists && result.upToDate;
    } catch {
      return false;
    }
  };

  // الاتصال بقاعدة البيانات وتهيئة الوكيل
  const handleConnect = async (connection: DatabaseConnection) => {
    try {
      setAppState('connecting');
      setCurrentConnection(connection);
      setError(null);

      // فحص Schema الموجود أولاً
      const hasValidSchema = await checkExistingSchema();

      if (hasValidSchema) {
        console.log('تم العثور على Schema محدث، تخطي عملية التحليل...');
        setAppState('ready');
        return;
      }

      console.log('لم يتم العثور على Schema محدث، بدء عملية التحليل...');
      setShowProgress(true);

      const response = await fetch('/api/database/extract', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(connection),
      });

      const result = await response.json();

      if (result.success) {
        setAgentState({
          isInitialized: true,
          hasSchema: true,
          totalTables: result.tablesCount || 0,
          schemaLastUpdated: new Date().toISOString(),
          databaseType: connection.type
        });

        // محاكاة بيانات الجداول (يمكن تحسينها لاحقاً)
        setTablesInfo([
          {
            name: 'users',
            description: 'جدول المستخدمين',
            domain: 'إدارة المستخدمين',
            columnsCount: 5,
            rowCount: 100
          },
          {
            name: 'products',
            description: 'جدول المنتجات',
            domain: 'التجارة الإلكترونية',
            columnsCount: 8,
            rowCount: 250
          }
        ]);

        setAppState('ready');
      } else {
        throw new Error(result.error || 'فشل في استخراج Schema');
      }

      setShowProgress(false);
    } catch (error) {
      console.error('خطأ في الاتصال:', error);
      setError(error instanceof Error ? error.message : 'خطأ غير معروف');
      setAppState('error');
      setShowProgress(false);
    }
  };

  // معالجة رسائل الدردشة
  const handleSendMessage = async (message: string): Promise<ChatMessage> => {
    try {
      const response = await fetch('/api/query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ question: message }),
      });

      const data = await response.json();

      if (data.success) {
        const result = data.result;
        console.log('نتيجة الاستعلام:', result); // للتشخيص
        setLastQueryResult(result);

        // التبديل التلقائي لتبويب النتائج إذا كانت هناك بيانات
        if (result.results && result.results.length > 0) {
          setActiveTab('results');
        }

        // تأكد من وجود البيانات وعرضها
        const hasResults = result.results && result.results.length > 0;

        return {
          id: `assistant-${Date.now()}`,
          type: 'assistant',
          content: result.error ?
            'عذراً، حدث خطأ أثناء معالجة سؤالك.' :
            hasResults ?
              `🎉 تم تنفيذ الاستعلام بنجاح! تم العثور على ${result.results.length} نتيجة. انتقل إلى تبويب "النتائج" لرؤية الجدول والرسم البياني.` :
              result.explanation || 'تم إنشاء الاستعلام بنجاح.',
          timestamp: new Date(),
          sqlQuery: result.query,
          explanation: result.explanation,
          confidence: result.confidence,
          relevantTables: result.relevantTables,
          executionTime: result.executionTime,
          error: result.error,
          results: result.results
        };
      } else {
        throw new Error(data.error || 'فشل في معالجة السؤال');
      }
    } catch (error) {
      return {
        id: `error-${Date.now()}`,
        type: 'system',
        content: 'حدث خطأ أثناء معالجة سؤالك.',
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      };
    }
  };

  // إعادة تعيين التطبيق
  const handleReset = () => {
    setAppState('setup');
    setCurrentConnection(null);
    setShowProgress(false);
    setLastQueryResult(null);
    setError(null);
    setActiveTab('chat');
    setAgentState({
      isInitialized: false,
      hasSchema: false,
      totalTables: 0
    });
    setTablesInfo([]);
  };

  // تشخيص الحالة الحالية
  console.log('🔍 حالة التطبيق:', appState, '| الاتصال:', currentConnection?.host, '| الوكيل:', agentState.isInitialized);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <Brain className="w-8 h-8 text-blue-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  الوكيل الذكي لقواعد البيانات
                </h1>
                <p className="text-sm text-gray-600">
                  نظام ذكاء صناعي متكامل لتحليل البيانات
                </p>
              </div>
            </div>

            {appState === 'ready' && (
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  متصل
                </div>
                <button
                  onClick={handleReset}
                  className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  إعادة تعيين
                </button>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {appState === 'setup' && (
          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                مرحباً بك في الوكيل الذكي
              </h2>
              <p className="text-gray-600">
                ابدأ بإعداد اتصال قاعدة البيانات لتحليل بياناتك بذكاء
              </p>
            </div>

            <DatabaseConfig
              onConnect={handleConnect}
              onTest={handleTestConnection}
              isConnecting={appState === 'connecting'}
            />
          </div>
        )}

        {appState === 'error' && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
              <div className="text-red-600 mb-4">
                <Database className="w-12 h-12 mx-auto mb-2" />
                <h3 className="text-lg font-medium">فشل في الاتصال</h3>
              </div>
              {error && (
                <p className="text-red-700 mb-4">{error}</p>
              )}
              <button
                onClick={handleReset}
                className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                المحاولة مرة أخرى
              </button>
            </div>
          </div>
        )}

        {appState === 'ready' && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-200px)]">
            {/* Sidebar */}
            <div className="lg:col-span-1 space-y-4">
              <DatabaseInfo
                agentState={agentState}
                tablesInfo={tablesInfo}
                onRefresh={() => window.location.reload()}
                onSettings={handleReset}
              />
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3 flex flex-col">
              {/* Tabs */}
              <div className="bg-white rounded-lg shadow-sm mb-4">
                <div className="flex border-b">
                  <button
                    onClick={() => setActiveTab('chat')}
                    className={`flex items-center gap-2 px-6 py-3 font-medium transition-colors ${
                      activeTab === 'chat'
                        ? 'text-blue-600 border-b-2 border-blue-600'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    <MessageSquare className="w-4 h-4" />
                    الدردشة
                  </button>

                  {lastQueryResult && (
                    <button
                      onClick={() => setActiveTab('results')}
                      className={`flex items-center gap-2 px-6 py-3 font-medium transition-colors ${
                        activeTab === 'results'
                          ? 'text-blue-600 border-b-2 border-blue-600'
                          : 'text-gray-600 hover:text-gray-800'
                      }`}
                    >
                      <BarChart3 className="w-4 h-4" />
                      النتائج
                    </button>
                  )}
                </div>
              </div>

              {/* Tab Content */}
              <div className="flex-1 bg-white rounded-lg shadow-sm overflow-hidden">
                {activeTab === 'chat' && (
                  <ChatInterface
                    onSendMessage={handleSendMessage}
                    isAgentReady={agentState.isInitialized}
                    className="h-full"
                  />
                )}

                {activeTab === 'results' && lastQueryResult && (
                  <div className="p-6 h-full overflow-y-auto">
                    {lastQueryResult.error ? (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                        <h3 className="text-red-800 font-medium mb-2">خطأ في تنفيذ الاستعلام</h3>
                        <p className="text-red-600">{lastQueryResult.error}</p>
                        <div className="mt-4 p-3 bg-gray-100 rounded text-sm">
                          <strong>الاستعلام:</strong>
                          <pre className="mt-1 text-gray-700">{lastQueryResult.query}</pre>
                        </div>
                      </div>
                    ) : (
                      <ResultsDisplay
                        data={lastQueryResult.results || []}
                        query={lastQueryResult.query}
                        explanation={lastQueryResult.explanation}
                        executionTime={lastQueryResult.executionTime}
                        intelligentSummary={lastQueryResult.intelligentSummary}
                      />
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </main>

      {/* Progress Modal */}
      {showProgress && (
        <SchemaProgress
          progress={{
            currentStep: 'جاري تحليل قاعدة البيانات...',
            totalSteps: 6,
            currentStepIndex: 1,
            tablesProcessed: 0,
            totalTables: 0,
            isComplete: false,
            logs: ['بدء عملية تحليل قاعدة البيانات...']
          }}
        />
      )}
    </div>
  );
}
