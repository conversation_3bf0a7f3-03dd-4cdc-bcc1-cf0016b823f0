// إدارة حدود الطلبات لـ Google Gemini API
export class RateLimiter {
  private static instance: RateLimiter;
  private lastRequestTime: number = 0;
  private requestCount: number = 0;
  private resetTime: number = 0;

  // حدود Google Gemini API (Free tier)
  private readonly REQUESTS_PER_MINUTE = 15; // 15 طلب في الدقيقة
  private readonly MIN_DELAY_BETWEEN_REQUESTS = 4000; // 4 ثواني بين الطلبات

  private constructor() {}

  static getInstance(): RateLimiter {
    if (!RateLimiter.instance) {
      RateLimiter.instance = new RateLimiter();
    }
    return RateLimiter.instance;
  }

  // انتظار قبل إرسال الطلب التالي
  async waitForNextRequest(): Promise<void> {
    const now = Date.now();
    
    // إعادة تعيين العداد كل دقيقة
    if (now - this.resetTime > 60000) {
      this.requestCount = 0;
      this.resetTime = now;
    }

    // التحقق من عدد الطلبات في الدقيقة
    if (this.requestCount >= this.REQUESTS_PER_MINUTE) {
      const waitTime = 60000 - (now - this.resetTime);
      console.log(`تم الوصول للحد الأقصى من الطلبات. انتظار ${Math.ceil(waitTime/1000)} ثانية...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      this.requestCount = 0;
      this.resetTime = Date.now();
    }

    // التحقق من التأخير بين الطلبات
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < this.MIN_DELAY_BETWEEN_REQUESTS) {
      const waitTime = this.MIN_DELAY_BETWEEN_REQUESTS - timeSinceLastRequest;
      console.log(`انتظار ${Math.ceil(waitTime/1000)} ثانية قبل الطلب التالي...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }

    this.lastRequestTime = Date.now();
    this.requestCount++;
  }

  // إعادة تعيين العداد في حالة الخطأ
  resetOnError(): void {
    // إضافة تأخير إضافي في حالة الخطأ
    this.lastRequestTime = Date.now() + 10000; // 10 ثواني إضافية
  }

  // الحصول على معلومات الحالة الحالية
  getStatus(): {
    requestCount: number;
    timeUntilReset: number;
    canMakeRequest: boolean;
  } {
    const now = Date.now();
    const timeUntilReset = Math.max(0, 60000 - (now - this.resetTime));
    const canMakeRequest = this.requestCount < this.REQUESTS_PER_MINUTE && 
                          (now - this.lastRequestTime) >= this.MIN_DELAY_BETWEEN_REQUESTS;

    return {
      requestCount: this.requestCount,
      timeUntilReset,
      canMakeRequest
    };
  }
}
