"use client"

import React, { useState } from 'react';
import { Database, TestTube, Save, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { DatabaseConnection, DatabaseType } from '@/lib/database/types';
import { cn } from '@/lib/utils';

interface DatabaseConfigProps {
  onConnect: (connection: DatabaseConnection) => Promise<void>;
  onTest: (connection: DatabaseConnection) => Promise<boolean>;
  isConnecting?: boolean;
  className?: string;
}

export function DatabaseConfig({ 
  onConnect, 
  onTest, 
  isConnecting = false,
  className = ""
}: DatabaseConfigProps) {
  const [config, setConfig] = useState<DatabaseConnection>({
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: '',
    password: '',
    database: ''
  });

  const [testStatus, setTestStatus] = useState<{
    status: 'idle' | 'testing' | 'success' | 'error';
    message?: string;
  }>({ status: 'idle' });

  const [errors, setErrors] = useState<Partial<Record<keyof DatabaseConnection, string>>>({});

  const handleTypeChange = (type: DatabaseType) => {
    setConfig(prev => ({
      ...prev,
      type,
      port: type === 'mysql' ? 3306 : 1433
    }));
    setTestStatus({ status: 'idle' });
    setErrors({});
  };

  const handleInputChange = (field: keyof DatabaseConnection, value: string | number) => {
    setConfig(prev => ({ ...prev, [field]: value }));
    
    // مسح الخطأ عند التعديل
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
    
    setTestStatus({ status: 'idle' });
  };

  const validateConfig = (): boolean => {
    const newErrors: Partial<Record<keyof DatabaseConnection, string>> = {};

    if (!config.host.trim()) {
      newErrors.host = 'عنوان الخادم مطلوب';
    }

    if (!config.username.trim()) {
      newErrors.username = 'اسم المستخدم مطلوب';
    }

    if (!config.database.trim()) {
      newErrors.database = 'اسم قاعدة البيانات مطلوب';
    }

    if (config.port < 1 || config.port > 65535) {
      newErrors.port = 'رقم المنفذ يجب أن يكون بين 1 و 65535';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleTest = async () => {
    if (!validateConfig()) return;

    setTestStatus({ status: 'testing' });

    try {
      const success = await onTest(config);
      if (success) {
        setTestStatus({ 
          status: 'success', 
          message: 'تم الاتصال بنجاح!' 
        });
      } else {
        setTestStatus({ 
          status: 'error', 
          message: 'فشل في الاتصال بقاعدة البيانات' 
        });
      }
    } catch (error) {
      setTestStatus({ 
        status: 'error', 
        message: error instanceof Error ? error.message : 'خطأ غير معروف' 
      });
    }
  };

  const handleConnect = async () => {
    if (!validateConfig()) return;

    try {
      await onConnect(config);
    } catch (error) {
      setTestStatus({ 
        status: 'error', 
        message: error instanceof Error ? error.message : 'خطأ في الاتصال' 
      });
    }
  };

  const getPortPlaceholder = () => {
    return config.type === 'mysql' ? '3306' : '1433';
  };

  return (
    <div className={cn("bg-white rounded-lg shadow-md p-6", className)}>
      <div className="flex items-center gap-3 mb-6">
        <Database className="w-6 h-6 text-blue-600" />
        <h2 className="text-xl font-semibold text-gray-900">إعدادات قاعدة البيانات</h2>
      </div>

      <div className="space-y-6">
        {/* Database Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            نوع قاعدة البيانات
          </label>
          <div className="grid grid-cols-2 gap-3">
            <button
              type="button"
              onClick={() => handleTypeChange('mysql')}
              className={cn(
                "p-4 border-2 rounded-lg transition-colors text-center",
                config.type === 'mysql'
                  ? "border-blue-500 bg-blue-50 text-blue-700"
                  : "border-gray-200 hover:border-gray-300 text-gray-700"
              )}
            >
              <div className="font-medium">MySQL</div>
              <div className="text-sm opacity-75">MySQL / MariaDB</div>
            </button>
            
            <button
              type="button"
              onClick={() => handleTypeChange('mssql')}
              className={cn(
                "p-4 border-2 rounded-lg transition-colors text-center",
                config.type === 'mssql'
                  ? "border-blue-500 bg-blue-50 text-blue-700"
                  : "border-gray-200 hover:border-gray-300 text-gray-700"
              )}
            >
              <div className="font-medium">SQL Server</div>
              <div className="text-sm opacity-75">Microsoft SQL Server</div>
            </button>
          </div>
        </div>

        {/* Connection Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Host */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              عنوان الخادم
            </label>
            <input
              type="text"
              value={config.host}
              onChange={(e) => handleInputChange('host', e.target.value)}
              placeholder="localhost"
              className={cn(
                "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
                errors.host ? "border-red-500" : "border-gray-300"
              )}
            />
            {errors.host && (
              <p className="mt-1 text-sm text-red-600">{errors.host}</p>
            )}
          </div>

          {/* Port */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              رقم المنفذ
            </label>
            <input
              type="number"
              value={config.port}
              onChange={(e) => handleInputChange('port', parseInt(e.target.value) || 0)}
              placeholder={getPortPlaceholder()}
              min="1"
              max="65535"
              className={cn(
                "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
                errors.port ? "border-red-500" : "border-gray-300"
              )}
            />
            {errors.port && (
              <p className="mt-1 text-sm text-red-600">{errors.port}</p>
            )}
          </div>

          {/* Username */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اسم المستخدم
            </label>
            <input
              type="text"
              value={config.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              placeholder="username"
              className={cn(
                "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
                errors.username ? "border-red-500" : "border-gray-300"
              )}
            />
            {errors.username && (
              <p className="mt-1 text-sm text-red-600">{errors.username}</p>
            )}
          </div>

          {/* Password */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              كلمة المرور
            </label>
            <input
              type="password"
              value={config.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              placeholder="password"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Database Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            اسم قاعدة البيانات
          </label>
          <input
            type="text"
            value={config.database}
            onChange={(e) => handleInputChange('database', e.target.value)}
            placeholder="database_name"
            className={cn(
              "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",
              errors.database ? "border-red-500" : "border-gray-300"
            )}
          />
          {errors.database && (
            <p className="mt-1 text-sm text-red-600">{errors.database}</p>
          )}
        </div>

        {/* Test Status */}
        {testStatus.status !== 'idle' && (
          <div className={cn(
            "p-4 rounded-lg border",
            testStatus.status === 'testing' && "bg-blue-50 border-blue-200",
            testStatus.status === 'success' && "bg-green-50 border-green-200",
            testStatus.status === 'error' && "bg-red-50 border-red-200"
          )}>
            <div className="flex items-center gap-2">
              {testStatus.status === 'testing' && (
                <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
              )}
              {testStatus.status === 'success' && (
                <CheckCircle className="w-4 h-4 text-green-600" />
              )}
              {testStatus.status === 'error' && (
                <AlertCircle className="w-4 h-4 text-red-600" />
              )}
              <span className={cn(
                "text-sm font-medium",
                testStatus.status === 'testing' && "text-blue-700",
                testStatus.status === 'success' && "text-green-700",
                testStatus.status === 'error' && "text-red-700"
              )}>
                {testStatus.status === 'testing' && "جاري اختبار الاتصال..."}
                {testStatus.status === 'success' && "نجح الاتصال"}
                {testStatus.status === 'error' && "فشل الاتصال"}
              </span>
            </div>
            {testStatus.message && (
              <p className={cn(
                "mt-1 text-sm",
                testStatus.status === 'testing' && "text-blue-600",
                testStatus.status === 'success' && "text-green-600",
                testStatus.status === 'error' && "text-red-600"
              )}>
                {testStatus.message}
              </p>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-3 pt-4 border-t">
          <button
            onClick={handleTest}
            disabled={testStatus.status === 'testing' || isConnecting}
            className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {testStatus.status === 'testing' ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <TestTube className="w-4 h-4" />
            )}
            اختبار الاتصال
          </button>

          <button
            onClick={handleConnect}
            disabled={testStatus.status !== 'success' || isConnecting}
            className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isConnecting ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            {isConnecting ? 'جاري الاتصال...' : 'الاتصال وبدء التحليل'}
          </button>
        </div>
      </div>
    </div>
  );
}
