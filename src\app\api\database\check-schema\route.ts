import { NextResponse } from 'next/server';
import { SchemaManager } from '@/lib/database/schema-manager';

export async function GET() {
  try {
    const schemaManager = SchemaManager.getInstance();
    
    // فحص وجود Schema محسن
    const enrichedSchema = await schemaManager.loadEnrichedSchema();
    
    if (!enrichedSchema) {
      return NextResponse.json({
        exists: false,
        upToDate: false,
        message: 'لا يوجد Schema محسن'
      });
    }

    // فحص ما إذا كان Schema أقدم من 7 أيام (أكثر مرونة)
    const extractedAt = new Date(enrichedSchema.extractedAt);
    const now = new Date();
    const hoursDiff = (now.getTime() - extractedAt.getTime()) / (1000 * 60 * 60);
    const upToDate = hoursDiff < (7 * 24); // 7 أيام

    // إعداد معلومات الجداول
    const tablesInfo = enrichedSchema.tables.map(table => ({
      name: table.tableName,
      description: table.description || 'لا يوجد وصف',
      domain: table.domain || 'عام',
      columnsCount: table.columns?.length || 0
    }));

    return NextResponse.json({
      exists: true,
      upToDate,
      extractedAt: enrichedSchema.extractedAt,
      tablesCount: enrichedSchema.tables.length,
      tablesInfo,
      hoursSinceExtraction: Math.round(hoursDiff),
      message: upToDate ?
        'Schema محدث ومتاح للاستخدام' :
        'Schema موجود لكنه قديم (أكثر من 7 أيام)'
    });

  } catch (error) {
    console.error('خطأ في فحص Schema:', error);
    return NextResponse.json({
      exists: false,
      upToDate: false,
      error: 'خطأ في فحص Schema'
    }, { status: 500 });
  }
}
