{"databaseName": "inventorymanagement", "databaseType": "mysql", "tables": [{"name": "customers", "columns": [{"name": "customer_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": 11}, {"name": "customer_name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 100}, {"name": "phone", "type": "<PERSON><PERSON><PERSON>(15)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 15}, {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 100}, {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 255}], "foreignKeys": [], "indexes": [{"name": "PRIMARY", "columns": ["customer_id"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["customer_id"], "rowCount": 86}, {"name": "invoice_details", "columns": [{"name": "invoice_detail_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": 11}, {"name": "invoice_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true, "maxLength": 11}, {"name": "item_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true, "maxLength": 11}, {"name": "quantity", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 11}, {"name": "unit_price", "type": "decimal(10,2)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "precision": 10, "scale": 2}, {"name": "subtotal", "type": "decimal(15,2)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "precision": 15, "scale": 2}], "foreignKeys": [{"columnName": "invoice_id", "referencedTable": "invoices", "referencedColumn": "invoice_id", "constraintName": "invoice_details_ibfk_1"}, {"columnName": "item_id", "referencedTable": "items", "referencedColumn": "item_id", "constraintName": "invoice_details_ibfk_2"}], "indexes": [{"name": "PRIMARY", "columns": ["invoice_detail_id"], "isUnique": true, "isPrimary": true}, {"name": "invoice_id", "columns": ["invoice_id"], "isUnique": false, "isPrimary": false}, {"name": "item_id", "columns": ["item_id"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["invoice_detail_id"], "rowCount": 150}, {"name": "invoices", "columns": [{"name": "invoice_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": 11}, {"name": "customer_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true, "maxLength": 11}, {"name": "invoice_date", "type": "date", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "total_amount", "type": "decimal(15,2)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "precision": 15, "scale": 2}, {"name": "notes", "type": "text", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}], "foreignKeys": [{"columnName": "customer_id", "referencedTable": "customers", "referencedColumn": "customer_id", "constraintName": "invoices_ibfk_1"}], "indexes": [{"name": "PRIMARY", "columns": ["invoice_id"], "isUnique": true, "isPrimary": true}, {"name": "customer_id", "columns": ["customer_id"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["invoice_id"], "rowCount": 100}, {"name": "items", "columns": [{"name": "item_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": 11}, {"name": "item_name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 100}, {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 50}, {"name": "unit_price", "type": "decimal(10,2)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "precision": 10, "scale": 2}, {"name": "cost_price", "type": "decimal(10,2)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "precision": 10, "scale": 2}, {"name": "stock_quantity", "type": "int(11)", "nullable": true, "defaultValue": "0", "isPrimaryKey": false, "isForeignKey": false, "maxLength": 11}], "foreignKeys": [], "indexes": [{"name": "PRIMARY", "columns": ["item_id"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["item_id"], "rowCount": 100}, {"name": "products", "columns": [{"name": "id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": 11}, {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 255}, {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 100}, {"name": "price", "type": "decimal(10,2)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "precision": 10, "scale": 2}, {"name": "stock", "type": "int(11)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 11}, {"name": "sales", "type": "int(11)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 11}], "foreignKeys": [], "indexes": [{"name": "PRIMARY", "columns": ["id"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["id"], "rowCount": 10}, {"name": "purchase_details", "columns": [{"name": "purchase_detail_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": 11}, {"name": "purchase_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true, "maxLength": 11}, {"name": "item_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true, "maxLength": 11}, {"name": "quantity", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 11}, {"name": "unit_cost", "type": "decimal(10,2)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "precision": 10, "scale": 2}, {"name": "subtotal", "type": "decimal(15,2)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "precision": 15, "scale": 2}], "foreignKeys": [{"columnName": "purchase_id", "referencedTable": "purchases", "referencedColumn": "purchase_id", "constraintName": "purchase_details_ibfk_1"}, {"columnName": "item_id", "referencedTable": "items", "referencedColumn": "item_id", "constraintName": "purchase_details_ibfk_2"}], "indexes": [{"name": "PRIMARY", "columns": ["purchase_detail_id"], "isUnique": true, "isPrimary": true}, {"name": "purchase_id", "columns": ["purchase_id"], "isUnique": false, "isPrimary": false}, {"name": "item_id", "columns": ["item_id"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["purchase_detail_id"], "rowCount": 150}, {"name": "purchases", "columns": [{"name": "purchase_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": 11}, {"name": "supplier_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true, "maxLength": 11}, {"name": "purchase_date", "type": "date", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "total_amount", "type": "decimal(15,2)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "precision": 15, "scale": 2}, {"name": "notes", "type": "text", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}], "foreignKeys": [{"columnName": "supplier_id", "referencedTable": "suppliers", "referencedColumn": "supplier_id", "constraintName": "purchases_ibfk_1"}], "indexes": [{"name": "PRIMARY", "columns": ["purchase_id"], "isUnique": true, "isPrimary": true}, {"name": "supplier_id", "columns": ["supplier_id"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["purchase_id"], "rowCount": 100}, {"name": "stock_movements", "columns": [{"name": "movement_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": 11}, {"name": "item_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": true, "maxLength": 11}, {"name": "movement_type", "type": "enum('شراء','بيع','إرجاع')", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "quantity", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 11}, {"name": "movement_date", "type": "date", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}, {"name": "remarks", "type": "text", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false}], "foreignKeys": [{"columnName": "item_id", "referencedTable": "items", "referencedColumn": "item_id", "constraintName": "stock_movements_ibfk_1"}], "indexes": [{"name": "PRIMARY", "columns": ["movement_id"], "isUnique": true, "isPrimary": true}, {"name": "item_id", "columns": ["item_id"], "isUnique": false, "isPrimary": false}], "primaryKeys": ["movement_id"], "rowCount": 1200}, {"name": "suppliers", "columns": [{"name": "supplier_id", "type": "int(11)", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": 11}, {"name": "supplier_name", "type": "<PERSON><PERSON><PERSON>(100)", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 100}, {"name": "contact_info", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 255}, {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 255}], "foreignKeys": [], "indexes": [{"name": "PRIMARY", "columns": ["supplier_id"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["supplier_id"], "rowCount": 50}], "relationships": [{"fromTable": "invoices", "fromColumn": "customer_id", "toTable": "customers", "toColumn": "customer_id", "relationshipType": "one-to-many", "constraintName": "invoices_ibfk_1"}, {"fromTable": "invoice_details", "fromColumn": "invoice_id", "toTable": "invoices", "toColumn": "invoice_id", "relationshipType": "one-to-many", "constraintName": "invoice_details_ibfk_1"}, {"fromTable": "invoice_details", "fromColumn": "item_id", "toTable": "items", "toColumn": "item_id", "relationshipType": "one-to-many", "constraintName": "invoice_details_ibfk_2"}, {"fromTable": "purchases", "fromColumn": "supplier_id", "toTable": "suppliers", "toColumn": "supplier_id", "relationshipType": "one-to-many", "constraintName": "purchases_ibfk_1"}, {"fromTable": "purchase_details", "fromColumn": "purchase_id", "toTable": "purchases", "toColumn": "purchase_id", "relationshipType": "one-to-many", "constraintName": "purchase_details_ibfk_1"}, {"fromTable": "purchase_details", "fromColumn": "item_id", "toTable": "items", "toColumn": "item_id", "relationshipType": "one-to-many", "constraintName": "purchase_details_ibfk_2"}, {"fromTable": "stock_movements", "fromColumn": "item_id", "toTable": "items", "toColumn": "item_id", "relationshipType": "one-to-many", "constraintName": "stock_movements_ibfk_1"}], "extractedAt": "2025-07-18T03:10:27.475Z", "version": "1.0"}