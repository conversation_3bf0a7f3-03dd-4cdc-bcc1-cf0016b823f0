// أمثلة استعلامات SQL صحيحة للاستعلامات الشائعة

export interface SQLExample {
  pattern: string;
  description: string;
  query: string;
  explanation: string;
  keywords: string[];
}

export const SQL_EXAMPLES: SQLExample[] = [
  {
    pattern: "أكثر العملاء شراءً",
    description: "العثور على أكثر العملاء شراءً حسب المبلغ الإجمالي",
    query: `SELECT 
  c.customer_name,
  SUM(id.quantity * id.unit_price) AS total_purchases
FROM customers c
JOIN invoices i ON c.customer_id = i.customer_id
JOIN invoice_details id ON i.invoice_id = id.invoice_id
GROUP BY c.customer_id, c.customer_name
ORDER BY total_purchases DESC
LIMIT 10;`,
    explanation: "يربط العملاء بفواتيرهم وتفاصيل الفواتير لحساب إجمالي المشتريات لكل عميل",
    keywords: ["أكثر", "العملاء", "شراء", "مشتريات", "أعلى"]
  },
  {
    pattern: "أكثر X عملاء شراءً",
    description: "العثور على أكثر عدد محدد من العملاء شراءً",
    query: `SELECT 
  c.customer_name,
  SUM(id.quantity * id.unit_price) AS total_purchases
FROM customers c
JOIN invoices i ON c.customer_id = i.customer_id
JOIN invoice_details id ON i.invoice_id = id.invoice_id
GROUP BY c.customer_id, c.customer_name
ORDER BY total_purchases DESC
LIMIT {X};`,
    explanation: "نفس الاستعلام السابق مع تحديد عدد النتائج المطلوبة",
    keywords: ["أكثر", "عملاء", "شراء", "ثلاثة", "خمسة", "عشرة"]
  },
  {
    pattern: "مشتريات عميل معين",
    description: "عرض جميع مشتريات عميل محدد",
    query: `SELECT 
  i.invoice_date,
  it.item_name,
  id.quantity,
  id.unit_price,
  (id.quantity * id.unit_price) AS subtotal
FROM customers c
JOIN invoices i ON c.customer_id = i.customer_id
JOIN invoice_details id ON i.invoice_id = id.invoice_id
JOIN items it ON id.item_id = it.item_id
WHERE c.customer_name = '{CUSTOMER_NAME}'
ORDER BY i.invoice_date DESC;`,
    explanation: "يعرض تفاصيل جميع مشتريات العميل المحدد مع تواريخ الشراء والمنتجات",
    keywords: ["مشتريات", "عميل", "فاطمة", "علي", "أحمد", "سارة"]
  },
  {
    pattern: "إجمالي مشتريات عميل",
    description: "حساب إجمالي مشتريات عميل معين",
    query: `SELECT 
  c.customer_name,
  COUNT(DISTINCT i.invoice_id) AS total_invoices,
  SUM(id.quantity * id.unit_price) AS total_amount
FROM customers c
JOIN invoices i ON c.customer_id = i.customer_id
JOIN invoice_details id ON i.invoice_id = id.invoice_id
WHERE c.customer_name = '{CUSTOMER_NAME}'
GROUP BY c.customer_id, c.customer_name;`,
    explanation: "يحسب إجمالي عدد الفواتير والمبلغ الإجمالي لمشتريات العميل",
    keywords: ["إجمالي", "مشتريات", "عميل", "مجموع"]
  },
  {
    pattern: "أكثر المنتجات مبيعاً",
    description: "العثور على أكثر المنتجات مبيعاً",
    query: `SELECT 
  it.item_name,
  SUM(id.quantity) AS total_sold,
  SUM(id.quantity * id.unit_price) AS total_revenue
FROM items it
JOIN invoice_details id ON it.item_id = id.item_id
GROUP BY it.item_id, it.item_name
ORDER BY total_sold DESC
LIMIT 10;`,
    explanation: "يعرض أكثر المنتجات مبيعاً حسب الكمية والإيرادات",
    keywords: ["أكثر", "المنتجات", "مبيعاً", "بيع", "منتج"]
  },
  {
    pattern: "مبيعات منتج معين",
    description: "عرض مبيعات منتج محدد",
    query: `SELECT
  i.invoice_date,
  it.item_name,
  id.quantity,
  id.unit_price,
  (id.quantity * id.unit_price) AS total_amount
FROM invoices i
JOIN invoice_details id ON i.invoice_id = id.invoice_id
JOIN items it ON id.item_id = it.item_id
WHERE it.item_name LIKE '%{PRODUCT_NAME}%'
ORDER BY i.invoice_date DESC;`,
    explanation: "يعرض جميع مبيعات المنتج المحدد مع تفاصيل التواريخ والكميات",
    keywords: ["مبيعات", "منتج", "برتقال", "تفاح", "موز", "سلعة", "بضاعة"]
  },
  {
    pattern: "إجمالي مبيعات منتج",
    description: "حساب إجمالي مبيعات منتج معين",
    query: `SELECT
  it.item_name,
  SUM(id.quantity) AS total_quantity,
  SUM(id.quantity * id.unit_price) AS total_revenue,
  COUNT(DISTINCT i.invoice_id) AS total_invoices,
  AVG(id.unit_price) AS avg_price
FROM invoices i
JOIN invoice_details id ON i.invoice_id = id.invoice_id
JOIN items it ON id.item_id = it.item_id
WHERE it.item_name LIKE '%{PRODUCT_NAME}%'
GROUP BY it.item_id, it.item_name;`,
    explanation: "يحسب إجمالي الكمية والإيرادات وعدد الفواتير للمنتج المحدد",
    keywords: ["إجمالي", "مبيعات", "منتج", "مجموع", "برتقال", "تفاح", "موز"]
  },
  {
    pattern: "مبيعات فترة زمنية",
    description: "عرض المبيعات خلال فترة زمنية محددة",
    query: `SELECT
  DATE(i.invoice_date) AS sale_date,
  COUNT(DISTINCT i.invoice_id) AS invoices_count,
  SUM(id.quantity * id.unit_price) AS daily_sales
FROM invoices i
JOIN invoice_details id ON i.invoice_id = id.invoice_id
WHERE i.invoice_date BETWEEN '{START_DATE}' AND '{END_DATE}'
GROUP BY DATE(i.invoice_date)
ORDER BY sale_date DESC;`,
    explanation: "يعرض المبيعات اليومية خلال فترة زمنية محددة",
    keywords: ["مبيعات", "فترة", "تاريخ", "يوم", "شهر", "سنة", "خلال"]
  },
  {
    pattern: "عملاء بدون مشتريات",
    description: "العثور على العملاء الذين لم يقوموا بأي مشتريات",
    query: `SELECT 
  c.customer_name,
  c.phone,
  c.email
FROM customers c
LEFT JOIN invoices i ON c.customer_id = i.customer_id
WHERE i.customer_id IS NULL;`,
    explanation: "يعرض العملاء المسجلين الذين لم يقوموا بأي مشتريات",
    keywords: ["عملاء", "بدون", "مشتريات", "لم", "يشتروا"]
  }
];

// دالة للبحث عن مثال مناسب بناءً على سؤال المستخدم
export function findRelevantExample(userQuestion: string): SQLExample | null {
  const questionLower = userQuestion.toLowerCase();

  // البحث عن أفضل مطابقة
  let bestMatch: SQLExample | null = null;
  let maxScore = 0;

  for (const example of SQL_EXAMPLES) {
    let score = 0;

    // البحث في الكلمات المفتاحية
    for (const keyword of example.keywords) {
      if (questionLower.includes(keyword.toLowerCase())) {
        score += 2;
      }
    }

    // البحث في النمط
    const patternWords = example.pattern.toLowerCase().split(' ');
    for (const word of patternWords) {
      if (questionLower.includes(word)) {
        score += 1;
      }
    }

    // إعطاء أولوية أعلى للمنتجات المحددة
    if (example.pattern.includes('منتج معين')) {
      const productNames = [
        'برتقال', 'تفاح', 'موز', 'عنب', 'فراولة', 'مانجو',
        'خيار', 'طماطم', 'بصل', 'جزر', 'خس', 'بقدونس',
        'أرز', 'سكر', 'ملح', 'زيت', 'دقيق', 'شاي', 'قهوة',
        'لحم', 'دجاج', 'سمك', 'بيض', 'حليب', 'جبن', 'زبدة'
      ];

      for (const product of productNames) {
        if (questionLower.includes(product)) {
          score += 5; // أولوية عالية للمنتجات المحددة
          break;
        }
      }

      // التحقق من وجود كلمة بعد "مبيعات"
      const salesMatch = questionLower.match(/مبيعات\s+(\w+)/);
      if (salesMatch && !questionLower.includes('فترة') && !questionLower.includes('تاريخ')) {
        score += 3;
      }
    }

    // تقليل النقاط للأمثلة العامة إذا كان السؤال محدد
    if (example.pattern.includes('فترة زمنية') &&
        (questionLower.match(/مبيعات\s+\w+/) &&
         !questionLower.includes('فترة') &&
         !questionLower.includes('تاريخ') &&
         !questionLower.includes('يوم') &&
         !questionLower.includes('شهر'))) {
      score -= 2;
    }

    if (score > maxScore) {
      maxScore = score;
      bestMatch = example;
    }
  }

  // إرجاع المثال إذا كان هناك تطابق جيد
  return maxScore >= 2 ? bestMatch : null;
}

// دالة لتخصيص الاستعلام بناءً على المعاملات
export function customizeQuery(query: string, params: Record<string, string>): string {
  let customizedQuery = query;
  
  for (const [key, value] of Object.entries(params)) {
    const placeholder = `{${key}}`;
    customizedQuery = customizedQuery.replace(new RegExp(placeholder, 'g'), value);
  }
  
  return customizedQuery;
}

// دالة لاستخراج المعاملات من سؤال المستخدم
export function extractParameters(userQuestion: string, pattern: string): Record<string, string> {
  const params: Record<string, string> = {};

  // استخراج الأرقام للحد الأقصى للنتائج
  const numberMatch = userQuestion.match(/(\d+)/);
  if (numberMatch && pattern.includes('{X}')) {
    params.X = numberMatch[1];
  }

  // استخراج أسماء العملاء
  const customerNames = ['فاطمة علي', 'أحمد محمد', 'سارة أحمد', 'محمد علي', 'نور الدين'];
  for (const name of customerNames) {
    if (userQuestion.includes(name)) {
      params.CUSTOMER_NAME = name;
      break;
    }
  }

  // استخراج أسماء المنتجات
  const productNames = [
    'برتقال', 'تفاح', 'موز', 'عنب', 'فراولة', 'مانجو', 'أناناس',
    'خيار', 'طماطم', 'بصل', 'جزر', 'خس', 'بقدونس', 'نعناع',
    'أرز', 'سكر', 'ملح', 'زيت', 'دقيق', 'شاي', 'قهوة',
    'لحم', 'دجاج', 'سمك', 'بيض', 'حليب', 'جبن', 'زبدة'
  ];

  for (const product of productNames) {
    if (userQuestion.includes(product)) {
      params.PRODUCT_NAME = product;
      break;
    }
  }

  // إذا لم نجد منتج محدد، نحاول استخراج كلمة بعد "مبيعات"
  if (!params.PRODUCT_NAME && pattern.includes('منتج')) {
    const salesMatch = userQuestion.match(/مبيعات\s+(\w+)/);
    if (salesMatch) {
      params.PRODUCT_NAME = salesMatch[1];
    }
  }

  return params;
}
