import mysql from 'mysql2/promise';
import { 
  DatabaseConnection, 
  TableInfo, 
  ColumnInfo, 
  ForeignKeyInfo, 
  IndexInfo,
  DatabaseSchema,
  RelationshipInfo 
} from './types';

export class MySQLSchemaExtractor {
  private connection: mysql.Connection | null = null;

  async connect(config: DatabaseConnection): Promise<void> {
    try {
      this.connection = await mysql.createConnection({
        host: config.host,
        port: config.port,
        user: config.username,
        password: config.password,
        database: config.database,
      });
      
      console.log('تم الاتصال بقاعدة بيانات MySQL بنجاح');
    } catch (error) {
      console.error('خطأ في الاتصال بقاعدة البيانات:', error);
      throw new Error(`فشل الاتصال بقاعدة البيانات: ${error}`);
    }
  }

  async disconnect(): Promise<void> {
    if (this.connection) {
      await this.connection.end();
      this.connection = null;
    }
  }

  async extractSchema(databaseName: string): Promise<DatabaseSchema> {
    if (!this.connection) {
      throw new Error('لا يوجد اتصال بقاعدة البيانات');
    }

    try {
      const tables = await this.getTables();
      const tablesWithDetails = await Promise.all(
        tables.map(tableName => this.getTableDetails(tableName))
      );

      const relationships = await this.getRelationships();

      return {
        databaseName,
        databaseType: 'mysql',
        tables: tablesWithDetails,
        relationships,
        extractedAt: new Date().toISOString(),
        version: '1.0'
      };
    } catch (error) {
      console.error('خطأ في استخراج schema:', error);
      throw error;
    }
  }

  private async getTables(): Promise<string[]> {
    const [rows] = await this.connection!.execute(
      'SHOW TABLES'
    ) as any[];
    
    return rows.map((row: any) => Object.values(row)[0] as string);
  }

  private async getTableDetails(tableName: string): Promise<TableInfo> {
    const columns = await this.getColumns(tableName);
    const foreignKeys = await this.getForeignKeys(tableName);
    const indexes = await this.getIndexes(tableName);
    const primaryKeys = await this.getPrimaryKeys(tableName);
    const rowCount = await this.getRowCount(tableName);

    return {
      name: tableName,
      columns,
      foreignKeys,
      indexes,
      primaryKeys,
      rowCount
    };
  }

  private async getColumns(tableName: string): Promise<ColumnInfo[]> {
    const [rows] = await this.connection!.execute(
      `DESCRIBE ${tableName}`
    ) as any[];

    return rows.map((row: any) => ({
      name: row.Field,
      type: row.Type,
      nullable: row.Null === 'YES',
      defaultValue: row.Default,
      isPrimaryKey: row.Key === 'PRI',
      isForeignKey: row.Key === 'MUL',
      maxLength: this.extractMaxLength(row.Type),
      precision: this.extractPrecision(row.Type),
      scale: this.extractScale(row.Type)
    }));
  }

  private async getForeignKeys(tableName: string): Promise<ForeignKeyInfo[]> {
    const [rows] = await this.connection!.execute(`
      SELECT 
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME,
        CONSTRAINT_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = ? 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    `, [tableName]) as any[];

    return rows.map((row: any) => ({
      columnName: row.COLUMN_NAME,
      referencedTable: row.REFERENCED_TABLE_NAME,
      referencedColumn: row.REFERENCED_COLUMN_NAME,
      constraintName: row.CONSTRAINT_NAME
    }));
  }

  private async getIndexes(tableName: string): Promise<IndexInfo[]> {
    const [rows] = await this.connection!.execute(
      `SHOW INDEX FROM ${tableName}`
    ) as any[];

    const indexMap = new Map<string, IndexInfo>();
    
    rows.forEach((row: any) => {
      const indexName = row.Key_name;
      if (!indexMap.has(indexName)) {
        indexMap.set(indexName, {
          name: indexName,
          columns: [],
          isUnique: row.Non_unique === 0,
          isPrimary: indexName === 'PRIMARY'
        });
      }
      indexMap.get(indexName)!.columns.push(row.Column_name);
    });

    return Array.from(indexMap.values());
  }

  private async getPrimaryKeys(tableName: string): Promise<string[]> {
    const [rows] = await this.connection!.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = ? 
        AND CONSTRAINT_NAME = 'PRIMARY'
      ORDER BY ORDINAL_POSITION
    `, [tableName]) as any[];

    return rows.map((row: any) => row.COLUMN_NAME);
  }

  private async getRowCount(tableName: string): Promise<number> {
    try {
      const [rows] = await this.connection!.execute(
        `SELECT COUNT(*) as count FROM ${tableName}`
      ) as any[];
      return rows[0].count;
    } catch {
      return 0;
    }
  }

  private async getRelationships(): Promise<RelationshipInfo[]> {
    const [rows] = await this.connection!.execute(`
      SELECT 
        TABLE_NAME as fromTable,
        COLUMN_NAME as fromColumn,
        REFERENCED_TABLE_NAME as toTable,
        REFERENCED_COLUMN_NAME as toColumn,
        CONSTRAINT_NAME as constraintName
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    `) as any[];

    return rows.map((row: any) => ({
      fromTable: row.fromTable,
      fromColumn: row.fromColumn,
      toTable: row.toTable,
      toColumn: row.toColumn,
      relationshipType: 'one-to-many' as const, // سيتم تحديد النوع لاحقاً
      constraintName: row.constraintName
    }));
  }

  private extractMaxLength(type: string): number | undefined {
    const match = type.match(/\((\d+)\)/);
    return match ? parseInt(match[1]) : undefined;
  }

  private extractPrecision(type: string): number | undefined {
    const match = type.match(/\((\d+),\d+\)/);
    return match ? parseInt(match[1]) : undefined;
  }

  private extractScale(type: string): number | undefined {
    const match = type.match(/\(\d+,(\d+)\)/);
    return match ? parseInt(match[1]) : undefined;
  }

  // تنفيذ استعلام SQL وإرجاع النتائج
  async executeQuery(query: string): Promise<any[]> {
    if (!this.connection) {
      throw new Error('لا يوجد اتصال نشط بقاعدة البيانات');
    }

    try {
      console.log('تنفيذ الاستعلام:', query);
      const [rows] = await this.connection.execute(query);
      return Array.isArray(rows) ? rows : [];
    } catch (error) {
      console.error('خطأ في تنفيذ الاستعلام:', error);
      throw new Error(`فشل في تنفيذ الاستعلام: ${error instanceof Error ? error.message : error}`);
    }
  }
}
