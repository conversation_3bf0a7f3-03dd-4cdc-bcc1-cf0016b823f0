import { NextRequest, NextResponse } from 'next/server';
import { MySQLSchemaExtractor } from '@/lib/database/mysql-extractor';
import { MSSQLSchemaExtractor } from '@/lib/database/mssql-extractor';
import { DatabaseConnection } from '@/lib/database/types';

export async function POST(request: NextRequest) {
  try {
    const connection: DatabaseConnection = await request.json();

    // التحقق من صحة البيانات
    if (!connection.host || !connection.username || !connection.database) {
      return NextResponse.json(
        { success: false, error: 'بيانات الاتصال غير مكتملة' },
        { status: 400 }
      );
    }

    // إنشاء المستخرج المناسب
    let extractor;
    if (connection.type === 'mysql') {
      extractor = new MySQLSchemaExtractor();
    } else if (connection.type === 'mssql') {
      extractor = new MSSQLSchemaExtractor();
    } else {
      return NextResponse.json(
        { success: false, error: 'نوع قاعدة البيانات غير مدعوم' },
        { status: 400 }
      );
    }

    // اختبار الاتصال
    await extractor.connect(connection);
    await extractor.disconnect();

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('خطأ في اختبار الاتصال:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'خطأ غير معروف' 
      },
      { status: 500 }
    );
  }
}
